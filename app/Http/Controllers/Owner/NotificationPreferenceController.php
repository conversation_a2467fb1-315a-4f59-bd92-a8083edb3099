<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Owner\Controller;
use App\Models\OwnerNotificationPreference;
use App\Services\OwnerNotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class NotificationPreferenceController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'business.isolation']);
    }

    /**
     * Display notification preferences.
     */
    public function index()
    {
        $business = $this->getUserBusiness();

        // Get or create default preferences for all notification types
        $preferences = collect(OwnerNotificationPreference::getNotificationTypes())
            ->map(function ($label, $type) use ($business) {
                return OwnerNotificationPreference::firstOrCreate([
                    'owner_id' => auth()->id(),
                    'business_id' => $business->id,
                    'notification_type' => $type
                ], array_merge(
                    OwnerNotificationPreference::getDefaultPreferences(),
                    ['notification_type' => $type]
                ));
            });

        // Get global settings summary
        $globalSettings = $this->getGlobalSettings($business);

        // Get notification statistics
        $stats = $this->getNotificationStats($business);

        return view('owner.notifications.preferences', compact('preferences', 'business', 'globalSettings', 'stats'));
    }

    /**
     * Update notification preferences.
     */
    public function update(Request $request)
    {
        $business = $this->getUserBusiness();

        // Validate the request data
        $validated = $request->validate([
            'preferences' => 'required|array',
            'preferences.*.notification_type' => 'required|string|in:' . implode(',', array_keys(OwnerNotificationPreference::getNotificationTypes())),
            'preferences.*.email_enabled' => 'boolean',
            'preferences.*.sms_enabled' => 'boolean',
            'preferences.*.push_enabled' => 'boolean',
            'preferences.*.in_app_enabled' => 'boolean',
            'preferences.*.sound_enabled' => 'boolean',
            'preferences.*.priority_filter' => 'nullable|in:low,normal,high,urgent',
            'preferences.*.quiet_hours_start' => 'nullable|date_format:H:i',
            'preferences.*.quiet_hours_end' => 'nullable|date_format:H:i',
            'preferences.*.weekend_notifications' => 'boolean',
            'preferences.*.digest_frequency' => 'nullable|in:never,daily,weekly,monthly',
            'preferences.*.auto_mark_read' => 'boolean',
        ]);

        try {
            DB::beginTransaction();

            foreach ($validated['preferences'] as $preferenceData) {
                OwnerNotificationPreference::updateOrCreate([
                    'owner_id' => auth()->id(),
                    'business_id' => $business->id,
                    'notification_type' => $preferenceData['notification_type']
                ], [
                    'email_enabled' => $preferenceData['email_enabled'] ?? false,
                    'sms_enabled' => $preferenceData['sms_enabled'] ?? false,
                    'push_enabled' => $preferenceData['push_enabled'] ?? false,
                    'in_app_enabled' => $preferenceData['in_app_enabled'] ?? false,
                    'sound_enabled' => $preferenceData['sound_enabled'] ?? true,
                    'priority_filter' => $preferenceData['priority_filter'] ?? null,
                    'quiet_hours_start' => $preferenceData['quiet_hours_start'] ?? null,
                    'quiet_hours_end' => $preferenceData['quiet_hours_end'] ?? null,
                    'weekend_notifications' => $preferenceData['weekend_notifications'] ?? true,
                    'digest_frequency' => $preferenceData['digest_frequency'] ?? 'daily',
                    'auto_mark_read' => $preferenceData['auto_mark_read'] ?? false,
                ]);
            }

            DB::commit();

            Log::info('Notification preferences updated successfully', [
                'owner_id' => auth()->id(),
                'business_id' => $business->id,
                'preferences_count' => count($validated['preferences'])
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Notification preferences updated successfully!',
                    'preferences' => $this->getFormattedPreferences($business)
                ]);
            }

            return redirect()->route('owner.notification-preferences.index')
                ->with('success', 'Notification preferences updated successfully!');

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to update notification preferences', [
                'owner_id' => auth()->id(),
                'business_id' => $business->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update notification preferences. Please try again.',
                    'error' => config('app.debug') ? $e->getMessage() : null
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Failed to update notification preferences. Please try again.')
                ->withInput();
        }
    }

    /**
     * Reset preferences to defaults.
     */
    public function reset(Request $request)
    {
        $business = $this->getUserBusiness();

        try {
            DB::beginTransaction();

            // Delete existing preferences
            OwnerNotificationPreference::where('owner_id', auth()->id())
                ->where('business_id', $business->id)
                ->delete();

            // Create default preferences
            OwnerNotificationPreference::createDefaultPreferences(auth()->id(), $business->id);

            DB::commit();

            Log::info('Notification preferences reset to defaults', [
                'owner_id' => auth()->id(),
                'business_id' => $business->id
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Notification preferences reset to defaults successfully!',
                    'preferences' => $this->getFormattedPreferences($business)
                ]);
            }

            return redirect()->route('owner.notification-preferences.index')
                ->with('success', 'Notification preferences reset to defaults successfully!');

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to reset notification preferences', [
                'owner_id' => auth()->id(),
                'business_id' => $business->id,
                'error' => $e->getMessage()
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to reset notification preferences. Please try again.'
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Failed to reset notification preferences. Please try again.');
        }
    }

    /**
     * Update global notification settings.
     */
    public function updateGlobalSettings(Request $request)
    {
        $business = $this->getUserBusiness();

        $validated = $request->validate([
            'global_email_enabled' => 'boolean',
            'global_sms_enabled' => 'boolean',
            'global_push_enabled' => 'boolean',
            'global_sound_enabled' => 'boolean',
            'global_quiet_hours_start' => 'nullable|date_format:H:i',
            'global_quiet_hours_end' => 'nullable|date_format:H:i',
            'global_weekend_notifications' => 'boolean',
            'global_digest_frequency' => 'required|in:never,daily,weekly,monthly',
        ]);

        try {
            DB::beginTransaction();

            // Update all preferences for this owner/business
            OwnerNotificationPreference::where('owner_id', auth()->id())
                ->where('business_id', $business->id)
                ->update([
                    'email_enabled' => $validated['global_email_enabled'] ?? false,
                    'sms_enabled' => $validated['global_sms_enabled'] ?? false,
                    'push_enabled' => $validated['global_push_enabled'] ?? false,
                    'sound_enabled' => $validated['global_sound_enabled'] ?? true,
                    'quiet_hours_start' => $validated['global_quiet_hours_start'],
                    'quiet_hours_end' => $validated['global_quiet_hours_end'],
                    'weekend_notifications' => $validated['global_weekend_notifications'] ?? true,
                    'digest_frequency' => $validated['global_digest_frequency'] ?? 'daily',
                ]);

            DB::commit();

            Log::info('Global notification settings updated', [
                'owner_id' => auth()->id(),
                'business_id' => $business->id,
                'settings' => $validated
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Global notification settings updated successfully!',
                'preferences' => $this->getFormattedPreferences($business)
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to update global notification settings', [
                'owner_id' => auth()->id(),
                'business_id' => $business->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update global notification settings. Please try again.'
            ], 500);
        }
    }

    /**
     * Test notification delivery.
     */
    public function testNotification(Request $request)
    {
        $business = $this->getUserBusiness();

        $validated = $request->validate([
            'notification_type' => 'required|string|in:' . implode(',', array_keys(OwnerNotificationPreference::getNotificationTypes())),
            'channels' => 'required|array|min:1',
            'channels.*' => 'in:email,sms,push,in_app'
        ]);

        try {
            // Create a test notification using the notification service
            $notificationService = app(OwnerNotificationService::class);

            $testData = [
                'test' => true,
                'timestamp' => now()->toISOString(),
                'channels' => $validated['channels']
            ];

            $notification = $notificationService->createNotification(
                $business->id,
                auth()->id(),
                $validated['notification_type'],
                'Test Notification - ' . ucfirst($validated['notification_type']),
                'This is a test notification to verify your settings are working correctly.',
                $testData,
                'normal'
            );

            Log::info('Test notification created', [
                'owner_id' => auth()->id(),
                'business_id' => $business->id,
                'notification_type' => $validated['notification_type'],
                'channels' => $validated['channels'],
                'notification_id' => $notification?->id
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Test notification sent successfully!',
                'notification_id' => $notification?->id
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send test notification', [
                'owner_id' => auth()->id(),
                'business_id' => $business->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send test notification. Please try again.'
            ], 500);
        }
    }

    /**
     * Get global settings summary.
     */
    private function getGlobalSettings($business)
    {
        $preferences = OwnerNotificationPreference::where('owner_id', auth()->id())
            ->where('business_id', $business->id)
            ->get();

        if ($preferences->isEmpty()) {
            return OwnerNotificationPreference::getDefaultPreferences();
        }

        // Get the most common settings across all notification types
        return [
            'email_enabled' => $preferences->where('email_enabled', true)->count() > 0,
            'sms_enabled' => $preferences->where('sms_enabled', true)->count() > 0,
            'push_enabled' => $preferences->where('push_enabled', true)->count() > 0,
            'in_app_enabled' => $preferences->where('in_app_enabled', true)->count() > 0,
            'sound_enabled' => $preferences->where('sound_enabled', true)->count() > 0,
            'weekend_notifications' => $preferences->where('weekend_notifications', true)->count() > 0,
            'quiet_hours_start' => $preferences->whereNotNull('quiet_hours_start')->first()?->quiet_hours_start,
            'quiet_hours_end' => $preferences->whereNotNull('quiet_hours_end')->first()?->quiet_hours_end,
            'digest_frequency' => $preferences->first()?->digest_frequency ?? 'daily',
        ];
    }

    /**
     * Get notification statistics.
     */
    private function getNotificationStats($business)
    {
        $preferences = OwnerNotificationPreference::where('owner_id', auth()->id())
            ->where('business_id', $business->id)
            ->get();

        return [
            'total_types' => $preferences->count(),
            'enabled_channels' => [
                'email' => $preferences->where('email_enabled', true)->count(),
                'sms' => $preferences->where('sms_enabled', true)->count(),
                'push' => $preferences->where('push_enabled', true)->count(),
                'in_app' => $preferences->where('in_app_enabled', true)->count(),
            ],
            'quiet_hours_configured' => $preferences->whereNotNull('quiet_hours_start')->whereNotNull('quiet_hours_end')->count(),
            'priority_filters_set' => $preferences->whereNotNull('priority_filter')->count(),
            'auto_mark_read_enabled' => $preferences->where('auto_mark_read', true)->count(),
        ];
    }

    /**
     * Get formatted preferences for API responses.
     */
    private function getFormattedPreferences($business)
    {
        return OwnerNotificationPreference::where('owner_id', auth()->id())
            ->where('business_id', $business->id)
            ->get()
            ->map(function ($preference) {
                return [
                    'id' => $preference->id,
                    'notification_type' => $preference->notification_type,
                    'type_label' => OwnerNotificationPreference::getNotificationTypes()[$preference->notification_type] ?? $preference->notification_type,
                    'email_enabled' => $preference->email_enabled,
                    'sms_enabled' => $preference->sms_enabled,
                    'push_enabled' => $preference->push_enabled,
                    'in_app_enabled' => $preference->in_app_enabled,
                    'sound_enabled' => $preference->sound_enabled,
                    'priority_filter' => $preference->priority_filter,
                    'quiet_hours_start' => $preference->quiet_hours_start?->format('H:i'),
                    'quiet_hours_end' => $preference->quiet_hours_end?->format('H:i'),
                    'weekend_notifications' => $preference->weekend_notifications,
                    'digest_frequency' => $preference->digest_frequency,
                    'auto_mark_read' => $preference->auto_mark_read,
                    'enabled_channels' => $preference->getEnabledChannels(),
                ];
            });
    }
}
