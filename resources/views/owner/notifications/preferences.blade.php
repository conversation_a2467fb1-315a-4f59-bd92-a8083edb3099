@extends('owner.layouts.app')

@section('title', 'Notification Preferences')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>Notification Preferences</h1>
            <p class="text-muted mb-0">Configure how and when you receive notifications for your business.</p>
        </div>
        <div class="d-flex" style="gap: 10px;">
            <button type="button" class="btn btn-outline-info" id="test-notification-btn">
                <i class="fas fa-paper-plane mr-1"></i>
                Test Notification
            </button>
            <button type="button" class="btn btn-outline-warning" id="reset-preferences-btn">
                <i class="fas fa-undo mr-1"></i>
                Reset to Defaults
            </button>
        </div>
    </div>
@stop

@section('content')
    {{-- Statistics Cards --}}
    <div class="row mb-4">
        <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3>{{ $stats['total_types'] }}</h3>
                    <p>Notification Types</p>
                </div>
                <div class="icon">
                    <i class="fas fa-bell"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3>{{ $stats['enabled_channels']['email'] }}</h3>
                    <p>Email Enabled</p>
                </div>
                <div class="icon">
                    <i class="fas fa-envelope"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3>{{ $stats['enabled_channels']['push'] }}</h3>
                    <p>Push Enabled</p>
                </div>
                <div class="icon">
                    <i class="fas fa-mobile-alt"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-danger">
                <div class="inner">
                    <h3>{{ $stats['quiet_hours_configured'] }}</h3>
                    <p>Quiet Hours Set</p>
                </div>
                <div class="icon">
                    <i class="fas fa-moon"></i>
                </div>
            </div>
        </div>
    </div>

    {{-- Global Settings Card --}}
    <div class="card mb-4">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-globe mr-2"></i>
                Global Settings
            </h3>
            <div class="card-tools">
                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label>Quiet Hours</label>
                        <div class="row">
                            <div class="col-6">
                                <input type="time" class="form-control" id="global_quiet_hours_start"
                                       value="{{ $globalSettings['quiet_hours_start'] ?? '' }}"
                                       placeholder="Start time">
                            </div>
                            <div class="col-6">
                                <input type="time" class="form-control" id="global_quiet_hours_end"
                                       value="{{ $globalSettings['quiet_hours_end'] ?? '' }}"
                                       placeholder="End time">
                            </div>
                        </div>
                        <small class="text-muted">No notifications will be sent during these hours</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>Digest Frequency</label>
                        <select class="form-control" id="global_digest_frequency">
                            <option value="never" {{ $globalSettings['digest_frequency'] === 'never' ? 'selected' : '' }}>Never</option>
                            <option value="daily" {{ $globalSettings['digest_frequency'] === 'daily' ? 'selected' : '' }}>Daily</option>
                            <option value="weekly" {{ $globalSettings['digest_frequency'] === 'weekly' ? 'selected' : '' }}>Weekly</option>
                            <option value="monthly" {{ $globalSettings['digest_frequency'] === 'monthly' ? 'selected' : '' }}>Monthly</option>
                        </select>
                        <small class="text-muted">How often to receive notification summaries</small>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <div class="form-group">
                        <div class="custom-control custom-switch">
                            <input type="checkbox" class="custom-control-input" id="global_weekend_notifications" 
                                   {{ $globalSettings['weekend_notifications'] ? 'checked' : '' }}>
                            <label class="custom-control-label" for="global_weekend_notifications">
                                Enable weekend notifications
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="text-right">
                <button type="button" class="btn btn-primary" id="save-global-settings-btn">
                    <i class="fas fa-save mr-1"></i>
                    Save Global Settings
                </button>
            </div>
        </div>
    </div>

    {{-- Notification Preferences Form --}}
    <form id="preferences-form" action="{{ route('owner.notification-preferences.update') }}" method="POST">
        @csrf
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-cogs mr-2"></i>
                    Notification Type Preferences
                </h3>
                <div class="card-tools">
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-primary" id="enable-all-email">
                            <i class="fas fa-envelope mr-1"></i>
                            Enable All Email
                        </button>
                        <button type="button" class="btn btn-outline-success" id="enable-all-push">
                            <i class="fas fa-mobile-alt mr-1"></i>
                            Enable All Push
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="thead-light">
                            <tr>
                                <th style="width: 25%;">Notification Type</th>
                                <th style="width: 15%;" class="text-center">Email</th>
                                <th style="width: 15%;" class="text-center">SMS</th>
                                <th style="width: 15%;" class="text-center">Push</th>
                                <th style="width: 15%;" class="text-center">In-App</th>
                                <th style="width: 15%;" class="text-center">Priority Filter</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($preferences as $preference)
                                <tr data-type="{{ $preference->notification_type }}">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="notification-icon mr-3">
                                                @php
                                                    $icons = [
                                                        'booking' => 'calendar-check',
                                                        'cancellation' => 'calendar-times',
                                                        'payment' => 'credit-card',
                                                        'review' => 'star',
                                                        'system' => 'cog',
                                                        'marketing' => 'bullhorn',
                                                        'alert' => 'exclamation-triangle',
                                                        'reminder' => 'clock',
                                                        'customer_message' => 'comment',
                                                        'waiting_list' => 'list'
                                                    ];
                                                    $icon = $icons[$preference->notification_type] ?? 'bell';
                                                @endphp
                                                <i class="fas fa-{{ $icon }} text-primary"></i>
                                            </div>
                                            <div>
                                                <strong>{{ OwnerNotificationPreference::getNotificationTypes()[$preference->notification_type] ?? $preference->notification_type }}</strong>
                                                <br>
                                                @php
                                                    $descriptions = [
                                                        'booking' => 'New bookings and appointment confirmations',
                                                        'cancellation' => 'Booking cancellations and modifications',
                                                        'payment' => 'Payment confirmations, failures, and refunds',
                                                        'review' => 'Customer reviews and ratings',
                                                        'system' => 'System maintenance and updates',
                                                        'marketing' => 'Promotional campaigns and announcements',
                                                        'alert' => 'Important alerts and warnings',
                                                        'reminder' => 'Appointment reminders and follow-ups',
                                                        'customer_message' => 'Messages from customers',
                                                        'waiting_list' => 'Waiting list updates and notifications'
                                                    ];
                                                    $description = $descriptions[$preference->notification_type] ?? 'General notifications';
                                                @endphp
                                                <small class="text-muted">{{ $description }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input channel-toggle" 
                                                   id="email_{{ $preference->notification_type }}" 
                                                   name="preferences[{{ $loop->index }}][email_enabled]"
                                                   value="1" {{ $preference->email_enabled ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="email_{{ $preference->notification_type }}"></label>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input channel-toggle" 
                                                   id="sms_{{ $preference->notification_type }}" 
                                                   name="preferences[{{ $loop->index }}][sms_enabled]"
                                                   value="1" {{ $preference->sms_enabled ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="sms_{{ $preference->notification_type }}"></label>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input channel-toggle" 
                                                   id="push_{{ $preference->notification_type }}" 
                                                   name="preferences[{{ $loop->index }}][push_enabled]"
                                                   value="1" {{ $preference->push_enabled ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="push_{{ $preference->notification_type }}"></label>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input channel-toggle" 
                                                   id="in_app_{{ $preference->notification_type }}" 
                                                   name="preferences[{{ $loop->index }}][in_app_enabled]"
                                                   value="1" {{ $preference->in_app_enabled ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="in_app_{{ $preference->notification_type }}"></label>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <select class="form-control form-control-sm" 
                                                name="preferences[{{ $loop->index }}][priority_filter]">
                                            <option value="">All</option>
                                            <option value="low" {{ $preference->priority_filter === 'low' ? 'selected' : '' }}>Low+</option>
                                            <option value="normal" {{ $preference->priority_filter === 'normal' ? 'selected' : '' }}>Normal+</option>
                                            <option value="high" {{ $preference->priority_filter === 'high' ? 'selected' : '' }}>High+</option>
                                            <option value="urgent" {{ $preference->priority_filter === 'urgent' ? 'selected' : '' }}>Urgent Only</option>
                                        </select>
                                    </td>
                                </tr>
                                {{-- Hidden fields for other preference data --}}
                                <input type="hidden" name="preferences[{{ $loop->index }}][notification_type]" value="{{ $preference->notification_type }}">
                                <input type="hidden" name="preferences[{{ $loop->index }}][sound_enabled]" value="1">
                                <input type="hidden" name="preferences[{{ $loop->index }}][weekend_notifications]" value="1">
                                <input type="hidden" name="preferences[{{ $loop->index }}][digest_frequency]" value="{{ $preference->digest_frequency }}">
                                <input type="hidden" name="preferences[{{ $loop->index }}][auto_mark_read]" value="{{ $preference->auto_mark_read ? '1' : '0' }}">
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="text-muted">
                        <small>
                            <i class="fas fa-info-circle mr-1"></i>
                            Changes are saved automatically. Last updated: <span id="last-updated">{{ now()->format('M j, Y g:i A') }}</span>
                        </small>
                    </div>
                    <button type="submit" class="btn btn-primary" id="save-preferences-btn">
                        <i class="fas fa-save mr-1"></i>
                        Save Preferences
                    </button>
                </div>
            </div>
        </div>
    </form>
@stop

@push('css')
<style>
.notification-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 123, 255, 0.1);
    border-radius: 50%;
}

.custom-switch .custom-control-input:checked ~ .custom-control-label::before {
    background-color: #28a745;
    border-color: #28a745;
}

.table td {
    vertical-align: middle;
}

.small-box {
    border-radius: 10px;
    transition: transform 0.2s;
}

.small-box:hover {
    transform: translateY(-2px);
}

.card {
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.btn-group-sm .btn {
    font-size: 0.875rem;
}

.form-control-sm {
    font-size: 0.875rem;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: 10px;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}
</style>
@endpush

@section('js')
<script>
$(document).ready(function() {
    // Auto-save functionality
    let autoSaveTimeout;

    // Track changes on form inputs
    $('#preferences-form input, #preferences-form select').on('change', function() {
        clearTimeout(autoSaveTimeout);
        autoSaveTimeout = setTimeout(function() {
            savePreferences();
        }, 1000); // Auto-save after 1 second of inactivity
    });

    // Manual save button
    $('#save-preferences-btn').on('click', function(e) {
        e.preventDefault();
        savePreferences();
    });

    // Save preferences function (similar to landing page pattern)
    function savePreferences() {
        const form = $('#preferences-form');
        const submitBtn = $('#save-preferences-btn');
        const originalText = submitBtn.html();

        // Show loading state
        submitBtn.html('<i class="fas fa-spinner fa-spin mr-1"></i>Saving...').prop('disabled', true);

        // Collect form data
        const formData = new FormData(form[0]);

        // Convert to preferences array format
        const preferences = [];
        const preferenceTypes = {!! json_encode(array_keys(\App\Models\OwnerNotificationPreference::getNotificationTypes())) !!};

        preferenceTypes.forEach(function(type, index) {
            preferences.push({
                notification_type: type,
                email_enabled: $(`#email_${type}`).is(':checked'),
                sms_enabled: $(`#sms_${type}`).is(':checked'),
                push_enabled: $(`#push_${type}`).is(':checked'),
                in_app_enabled: $(`#in_app_${type}`).is(':checked'),
                sound_enabled: true,
                priority_filter: $(`select[name="preferences[${index}][priority_filter]"]`).val() || null,
                quiet_hours_start: null,
                quiet_hours_end: null,
                weekend_notifications: true,
                digest_frequency: 'daily',
                auto_mark_read: false
            });
        });

        // AJAX request
        $.ajax({
            url: form.attr('action'),
            method: 'POST',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content'),
                preferences: preferences
            },
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            timeout: 30000,
            success: function(response) {
                console.log('Success response:', response);
                toastr.success(response.message || 'Notification preferences updated successfully!');

                // Update last updated time
                $('#last-updated').text(new Date().toLocaleDateString() + ' at ' + new Date().toLocaleTimeString());

                // Update statistics if provided
                if (response.preferences) {
                    updateStatistics(response.preferences);
                }
            },
            error: function(xhr, textStatus, errorThrown) {
                console.error('AJAX Error:', {
                    status: xhr.status,
                    statusText: xhr.statusText,
                    textStatus: textStatus,
                    errorThrown: errorThrown,
                    responseText: xhr.responseText,
                    response: xhr.responseJSON
                });

                if (xhr.status === 422) {
                    // Validation errors
                    const errors = xhr.responseJSON?.errors;
                    if (errors) {
                        let errorMessage = 'Please fix the following errors:\n';
                        Object.keys(errors).forEach(function(key) {
                            errorMessage += '- ' + errors[key][0] + '\n';
                        });
                        toastr.error(errorMessage);
                    } else {
                        toastr.error('Validation error occurred. Please check your input and try again.');
                    }
                } else if (xhr.status === 500) {
                    toastr.error('Server error occurred. Please try again.');
                } else {
                    toastr.error('An error occurred while saving preferences. Please try again.');
                }
            },
            complete: function() {
                // Restore button state
                submitBtn.html(originalText).prop('disabled', false);
            }
        });
    }

    // Global settings save
    $('#save-global-settings-btn').on('click', function() {
        const btn = $(this);
        const originalText = btn.html();

        btn.html('<i class="fas fa-spinner fa-spin mr-1"></i>Saving...').prop('disabled', true);

        $.ajax({
            url: '{{ route("owner.notification-preferences.global-settings") }}',
            method: 'POST',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content'),
                global_email_enabled: true, // Will be handled by individual preferences
                global_sms_enabled: true,
                global_push_enabled: true,
                global_sound_enabled: true,
                global_quiet_hours_start: $('#global_quiet_hours_start').val(),
                global_quiet_hours_end: $('#global_quiet_hours_end').val(),
                global_weekend_notifications: $('#global_weekend_notifications').is(':checked'),
                global_digest_frequency: $('#global_digest_frequency').val()
            },
            success: function(response) {
                toastr.success(response.message || 'Global settings updated successfully!');
            },
            error: function(xhr) {
                console.error('Global settings error:', xhr);
                toastr.error('Failed to update global settings. Please try again.');
            },
            complete: function() {
                btn.html(originalText).prop('disabled', false);
            }
        });
    });

    // Bulk enable/disable functions
    $('#enable-all-email').on('click', function() {
        $('input[id^="email_"]').prop('checked', true).trigger('change');
        toastr.info('All email notifications enabled');
    });

    $('#enable-all-push').on('click', function() {
        $('input[id^="push_"]').prop('checked', true).trigger('change');
        toastr.info('All push notifications enabled');
    });

    // Test notification
    $('#test-notification-btn').on('click', function() {
        showTestNotificationModal();
    });

    // Reset preferences
    $('#reset-preferences-btn').on('click', function() {
        if (confirm('Are you sure you want to reset all notification preferences to defaults? This action cannot be undone.')) {
            resetPreferences();
        }
    });

    function resetPreferences() {
        $.ajax({
            url: '{{ route("owner.notification-preferences.reset") }}',
            method: 'POST',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                toastr.success(response.message || 'Preferences reset successfully!');
                setTimeout(function() {
                    window.location.reload();
                }, 1500);
            },
            error: function(xhr) {
                console.error('Reset error:', xhr);
                toastr.error('Failed to reset preferences. Please try again.');
            }
        });
    }

    function showTestNotificationModal() {
        // Create a simple test notification modal
        const modal = `
            <div class="modal fade" id="testNotificationModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">Test Notification</h4>
                            <button type="button" class="close" data-dismiss="modal">&times;</button>
                        </div>
                        <div class="modal-body">
                            <div class="form-group">
                                <label>Notification Type</label>
                                <select class="form-control" id="test-notification-type">
                                    ${Object.entries({!! json_encode(\App\Models\OwnerNotificationPreference::getNotificationTypes()) !!}).map(([key, label]) =>
                                        `<option value="${key}">${label}</option>`
                                    ).join('')}
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Test Channels</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="test-email" value="email" checked>
                                    <label class="form-check-label" for="test-email">Email</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="test-push" value="push" checked>
                                    <label class="form-check-label" for="test-push">Push Notification</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="test-in-app" value="in_app" checked>
                                    <label class="form-check-label" for="test-in-app">In-App Notification</label>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" id="send-test-notification">Send Test</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal and add new one
        $('#testNotificationModal').remove();
        $('body').append(modal);
        $('#testNotificationModal').modal('show');

        // Handle test notification sending
        $('#send-test-notification').on('click', function() {
            const btn = $(this);
            const originalText = btn.text();

            btn.text('Sending...').prop('disabled', true);

            const channels = [];
            $('#testNotificationModal input[type="checkbox"]:checked').each(function() {
                channels.push($(this).val());
            });

            $.ajax({
                url: '{{ route("owner.notification-preferences.test") }}',
                method: 'POST',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content'),
                    notification_type: $('#test-notification-type').val(),
                    channels: channels
                },
                success: function(response) {
                    toastr.success(response.message || 'Test notification sent successfully!');
                    $('#testNotificationModal').modal('hide');
                },
                error: function(xhr) {
                    console.error('Test notification error:', xhr);
                    toastr.error('Failed to send test notification. Please try again.');
                },
                complete: function() {
                    btn.text(originalText).prop('disabled', false);
                }
            });
        });
    }

    function updateStatistics(preferences) {
        // Update statistics cards based on current preferences
        const emailEnabled = preferences.filter(p => p.email_enabled).length;
        const pushEnabled = preferences.filter(p => p.push_enabled).length;

        $('.small-box.bg-success .inner h3').text(emailEnabled);
        $('.small-box.bg-warning .inner h3').text(pushEnabled);
    }
});
</script>
@endsection
